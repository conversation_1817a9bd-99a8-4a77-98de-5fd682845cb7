# 极光车融合版 v1.1

基于MaixPy的智能图像处理系统，支持矩形检测、激光点检测和实时二值化效果显示。

## 主要功能

### 1. 矩形检测
- 实时检测图像中的规则矩形
- 支持多种过滤条件：面积、宽高比、角度、对边一致性
- 透视变换校正和中心点计算
- 圆形轨迹生成

### 2. 二值化效果实时显示 ⭐ **新功能**
- 在界面右上角显示实时二值化处理效果
- 与主显示画面保持相同宽高比（160x120，主画面的一半）
- 实时响应阈值参数调整
- 清晰的功能标识和边框

### 3. 交互控制
- 触摸屏支持，防抖动处理
- 虚拟按键：Center/Circle模式切换、阈值调节（T+/T-）
- 实时FPS显示和状态信息

### 4. 串口通信
- UART数据传输
- 支持中心点和圆形轨迹数据发送

## 界面布局

```
┌─────────────────────┬─────────────────┐
│                     │  Binary Effect  │
│   主显示画面         │   (160x120)     │
│   (320x240)         │   二值化效果     │
│                     │                 │
│                     └─────────────────┤
│                                       │
│  [Center] [Circle] [T-] [T+]         │
│   虚拟按键区域                         │
└───────────────────────────────────────┘
```

## 配置参数

所有关键参数都集中在代码开头的配置区域，便于调整：

### 矩形检测参数
- `MIN_CONTOUR_AREA`: 最小轮廓面积 (500)
- `MAX_CONTOUR_AREA`: 最大轮廓面积 (60000)
- `BINARY_THRESHOLD`: 二值化阈值 (66)
- `MIN_ASPECT_RATIO`: 最小宽高比 (0.6)
- `MAX_ASPECT_RATIO`: 最大宽高比 (1.7)

### 二值化显示参数 ⭐ **新增**
- `BINARY_DISPLAY_WIDTH`: 显示区域宽度 (160)
- `BINARY_DISPLAY_HEIGHT`: 显示区域高度 (120)
- `BINARY_DISPLAY_X`: 显示区域X坐标 (320)
- `BINARY_DISPLAY_Y`: 显示区域Y坐标 (0)

## 使用方法

### 基本操作
1. 启动程序后，主画面显示摄像头实时图像
2. 右上角同步显示二值化处理效果
3. 使用触摸屏点击虚拟按键进行控制

### 阈值调节
- 点击 **T+** 按钮：增加二值化阈值（+3）
- 点击 **T-** 按钮：减少二值化阈值（-3）
- 二值化效果区域会实时更新显示处理结果

### 模式切换
- **Center模式**：检测矩形中心点
- **Circle模式**：生成圆形轨迹点

## 技术特性

- **高效处理**：优化的图像处理算法，保持流畅帧率
- **实时响应**：二值化效果与参数调整同步更新
- **用户友好**：直观的双画面对比显示
- **参数可调**：所有关键参数支持运行时调整
- **稳定通信**：可靠的串口数据传输

## 更新日志

### v1.1 ⭐ **最新版本**
- **新增**：右上角二值化效果实时显示区域
- **新增**：二值化显示区域配置参数
- **优化**：扩展画布布局，支持双画面显示
- **改进**：更直观的参数调节效果预览

### v1.0
- 基础矩形检测功能
- 触摸屏交互控制
- 串口通信支持

## 开发环境

- **平台**：MaixPy
- **依赖**：OpenCV, NumPy
- **硬件**：支持摄像头和触摸屏的MaixPy设备

## 文件结构

```
├── main.py                 # 主程序文件
├── micu_uart_lib/         # 串口通信库
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── simple_uart.py     # 串口通信
│   └── utils.py           # 工具函数
├── test_binary_display.py # 二值化显示测试脚本
└── README.md              # 项目说明文档
```

## 注意事项

1. 确保设备支持触摸屏功能
2. 串口设备路径可能需要根据实际硬件调整
3. 二值化阈值建议在50-150范围内调节
4. 扩展画布总尺寸为480x240像素
