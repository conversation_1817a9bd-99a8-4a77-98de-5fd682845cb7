#!/usr/bin/env python3
"""
测试二值化显示功能的简单脚本
用于验证新增的二值化显示区域是否正常工作
"""

import cv2
import numpy as np

# 模拟配置参数
BINARY_DISPLAY_WIDTH = 160   # 二值化显示区域宽度（主画面一半）
BINARY_DISPLAY_HEIGHT = 120  # 二值化显示区域高度（主画面一半）
BINARY_DISPLAY_X = 320       # 二值化显示区域X坐标（主画面右侧）
BINARY_DISPLAY_Y = 0         # 二值化显示区域Y坐标（顶部）

def test_binary_display():
    """测试二值化显示功能"""
    
    # 创建模拟图像（320x240）
    img = np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8)
    
    # 创建扩展画布
    extended_width = 320 + BINARY_DISPLAY_WIDTH
    extended_height = max(240, BINARY_DISPLAY_HEIGHT)
    extended_output = np.zeros((extended_height, extended_width, 3), dtype=np.uint8)
    extended_output[0:240, 0:320] = img
    
    # 模拟二值化处理
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    binary_threshold = 128
    _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
    
    # 创建二值化显示图像
    binary_resized = cv2.resize(binary, (BINARY_DISPLAY_WIDTH, BINARY_DISPLAY_HEIGHT))
    binary_colored = cv2.cvtColor(binary_resized, cv2.COLOR_GRAY2BGR)
    
    # 将二值化图像放置到扩展画布的右上角
    extended_output[BINARY_DISPLAY_Y:BINARY_DISPLAY_Y+BINARY_DISPLAY_HEIGHT, 
                   BINARY_DISPLAY_X:BINARY_DISPLAY_X+BINARY_DISPLAY_WIDTH] = binary_colored
    
    # 添加边框和标签
    cv2.rectangle(extended_output, 
                 (BINARY_DISPLAY_X, BINARY_DISPLAY_Y), 
                 (BINARY_DISPLAY_X+BINARY_DISPLAY_WIDTH, BINARY_DISPLAY_Y+BINARY_DISPLAY_HEIGHT), 
                 (0, 255, 255), 2)
    cv2.putText(extended_output, "Binary Effect", 
               (BINARY_DISPLAY_X+5, BINARY_DISPLAY_Y+15),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    
    # 添加一些测试信息
    cv2.putText(extended_output, f"Original: 320x240", (10, 20),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    cv2.putText(extended_output, f"Binary: {BINARY_DISPLAY_WIDTH}x{BINARY_DISPLAY_HEIGHT}", 
               (BINARY_DISPLAY_X+5, BINARY_DISPLAY_Y+35),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    cv2.putText(extended_output, f"Threshold: {binary_threshold}", 
               (BINARY_DISPLAY_X+5, BINARY_DISPLAY_Y+55),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    
    print(f"扩展画布尺寸: {extended_width}x{extended_height}")
    print(f"主画面区域: 320x240")
    print(f"二值化显示区域: {BINARY_DISPLAY_WIDTH}x{BINARY_DISPLAY_HEIGHT}")
    print(f"二值化区域位置: ({BINARY_DISPLAY_X}, {BINARY_DISPLAY_Y})")
    
    # 保存测试图像
    cv2.imwrite("test_binary_display_result.png", extended_output)
    print("测试图像已保存为: test_binary_display_result.png")
    
    return True

if __name__ == "__main__":
    print("开始测试二值化显示功能...")
    if test_binary_display():
        print("测试完成！")
    else:
        print("测试失败！")
